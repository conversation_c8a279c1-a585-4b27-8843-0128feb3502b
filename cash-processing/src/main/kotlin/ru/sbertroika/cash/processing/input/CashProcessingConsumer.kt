package ru.sbertroika.cash.processing.input

import arrow.core.Either
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.cash.processing.service.CashService
import ru.sbertroika.cash.processing.util.mapper
import ru.sbertroika.tkp3.cash.api.model.CashTap
import ru.sbertroika.tkp3.cash.model.CashTrx
import java.time.ZoneId
import java.time.ZonedDateTime

@Component
class CashProcessingConsumer(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    private val cashService: CashService,

    @Value("\${spring.kafka.pro_error_out_topic}")
    private val errorTopic: String,
    @Value("\${spring.kafka.cash_trx_in_topic}")
    private val trxTopic: String,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    @KafkaListener(groupId = "\${spring.kafka.cash_processing_in_group}", topics = ["\${spring.kafka.pro_cash_in_topic}"])
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            toOperationMessage(record.value())
                .fold(
                    {
                        logger.error("error process operation ${record.key()}: ${it.message}")
                        val out = ProducerRecord<String, Any>(errorTopic, record.key(), mapper.writeValueAsString(it.message))
                        producer.send(out)
                        acknowledgment.acknowledge()
                    },
                    { operation ->
                        cashService.process(operation.trxId, operation).fold(
                            {
                                logger.error("error process operation ${record.key()}: ${it.message}")
                                val out = ProducerRecord<String, Any>(errorTopic, record.key(), mapper.writeValueAsString(it.message))
                                producer.send(out)
                                acknowledgment.acknowledge()
                            },
                            {
                                pushCashTrx(
                                    CashTrx(
                                        trxId = operation.trxId,
                                        createdAt = operation.createdAt,
                                        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
                                        projectId = operation.projectId,
                                        ern = operation.ern,
                                        shiftNum = operation.shiftNum,
                                        orgId = operation.orgId,
                                        routeNumber = operation.routeNumber,
                                        vehicleNumber = operation.vehicleNumber,
                                        cashierName = operation.cashierName,
                                        driverName = operation.driverName,
                                        amount = operation.amount,
                                        ticketCount = operation.ticketCount,
                                        terminalSerial = operation.terminalSerial
                                    )
                                )
                                acknowledgment.acknowledge()
                            }
                        )
                    })
        } catch (e: Exception) {
            logger.error("error process transaction ${record.key()}", e)
            acknowledgment.acknowledge()
        }
    }

    private fun toOperationMessage(data: String): Either<Throwable, CashTap> = Either.catch {
        mapper.readValue<CashTap>(data)
    }

    private fun pushCashTrx(trx: CashTrx) = Either.catch {
        val out = ProducerRecord<String, Any>(trxTopic, trx.trxId.toString(), mapper.writeValueAsString(trx))
        producer.send(out)
    }
}