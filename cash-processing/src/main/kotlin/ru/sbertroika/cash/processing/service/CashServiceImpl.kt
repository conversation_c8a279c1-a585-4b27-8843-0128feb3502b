package ru.sbertroika.cash.processing.service

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.cash.processing.ProductNotFound
import ru.sbertroika.cash.processing.VehicleNotFound
import ru.sbertroika.cash.processing.output.repository.ProductRepository
import ru.sbertroika.cash.processing.output.repository.VehicleRepository
import ru.sbertroika.fiscalapi.model.*
import ru.sbertroika.tkp3.cash.api.model.CashTap
import ru.sbertroika.tkp3.pro.model.VehicleType.*
import java.util.*

@Service
class CashServiceImpl(
    private val ticketService: TicketService,
    private val fiscalService: FiscalService,
    private val vehicleRepository: VehicleRepository,
    private val productRepository: ProductRepository
) : CashService {

    override suspend fun process(id: UUID, message: CashTap): Either<Throwable, Unit> = Either.catch {
        //TODO сделать проверку что у организации включена фискализация

        ticketService.findTickets(message.trxId).fold(
            {
                throw it
            },
            { tickets ->
                tickets.forEach { ticket ->
                    val vehicle = vehicleRepository.findByIdAndVersion(ticket.vehicleId!!, ticket.vehicleVersion!!)
                        ?: throw VehicleNotFound("vehicleId=${ticket.vehicleId}, vehicleVersion=${ticket.vehicleVersion}")

                    val product = productRepository.findByIdAndVersion(ticket.productId!!, ticket.productVersion!!)
                        ?: throw ProductNotFound("productId=${ticket.productId}, productVersion=${ticket.productVersion}")

                    val price = ticket.amount!!.toDouble() / 100
                    fiscalService.sendCheck(
                        FiscalInputSell(
                            scheme = ticket.orgId.toString(),
                            orderId = "TTV1-${ticket.ticketSeries}-${ticket.ticketNumber}",
                            client = Client(
                                phone = null,
                                email = "<EMAIL>"
                            ),
                            agent = null,
                            agentInfo = null,
                            contractor = Contractor(
                                taxationSystem = TaxSystemType.OSN,
                                paymentPlace = when (vehicle.type) {
                                    BUS -> "Автобус ${vehicle.number}"
                                    TRAM -> "Трамвай ${vehicle.number}"
                                    TROLLEYBUS -> "Троллейбус ${vehicle.number}"
                                    METRO -> "Метро"
                                    null -> null
                                },
                                terminalNumber = ticket.terminalSerial
                            ),
                            items = listOf(
                                OrderItems(
                                    name = product.name!!,
                                    price = price,
                                    quantity = 1.0,
                                    sum = price,
                                    tax = Tax(
                                        type = TaxType.NONE,
                                        sum = 0.0
                                    )
                                )
                            ),
                            payments = listOf(
                                Payment(
                                    type = PaymentType.CASH,
                                    sum = price
                                )
                            ),
                            total = price,
                            cashier = message.cashierName.ifEmpty { message.driverName },
                            cashierInn = null
                        )
                    )
                }
            }
        )
    }
}