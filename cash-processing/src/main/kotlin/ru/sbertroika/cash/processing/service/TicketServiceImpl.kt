package ru.sbertroika.cash.processing.service

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.sbertroika.cash.processing.TicketNotFound
import ru.sbertroika.tkp3.pro.model.Ticket
import java.util.*

@Service
class TicketServiceImpl(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) : TicketService {

    override suspend fun findTickets(trxId: UUID): Either<Throwable, List<Ticket>> = Either.catch {
        val search = mutableListOf<Criteria>()

        search.add(Criteria.where("trxId").`is`(trxId))

        withContext(Dispatchers.IO) {
            entityTemplate.select(Ticket::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(Sort.by("createdAt").descending())
                        .limit(1)
                ).all()
                .collectList()
                .block()
        } ?: throw TicketNotFound()
    }
}