package ru.sbertroika.cash.processing.service

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.cash.processing.util.mapper
import ru.sbertroika.fiscalapi.model.FiscalInput

@Service
class FiscalServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,

    @Value("\${spring.kafka.fiscal_in_topic}")
    private val fiscalInTopic: String
) : FiscalService {
    private val producer = kafkaProducerFactory.createProducer()
    private val mapper = mapper()

    override suspend fun sendCheck(request: FiscalInput): Either<Throwable, Unit> = Either.catch {
        val out = ProducerRecord<String, Any>(fiscalInTopic, request.orderId, mapper.writeValueAsString(request))
        producer.send(out)
    }
}