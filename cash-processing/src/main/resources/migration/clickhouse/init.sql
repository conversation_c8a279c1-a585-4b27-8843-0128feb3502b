CREATE TABLE cash_trx
(
    trx_id     UUID comment 'Идентификатор транзакции (заказа)',
    created_at DateTime('UTC') comment 'Время формирования транзакции на терминале',
    record_at  DateTime('UTC') comment 'Время формирования транзакции на сервере',

    tags       Nullable(String) comment 'Тэги'
) engine = MergeTree PARTITION BY toYYYYMM(created_at)
      PRIMARY KEY (trx_id)
      SETTINGS index_granularity = 8192;

CREATE TABLE kafka_cash_trx
(
    trxId     String,
    createdAt String,
    recordAt  String,
    tags      Nullable(String)
) ENGINE = Kafka('10.4.32.25:9092,10.4.32.140:9092,10.4.32.88:9092', 'CASH.TRX', 'cash_trx_loader',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW cash_trx_mv TO cash_trx as
SELECT toUUID(trxId)                             as trx_id,
       parseDateTimeBestEffort(createdAt, 'UTC') as created_at,
       parseDateTimeBestEffort(recordAt, 'UTC')  as record_at,
       ''                                        as tags
FROM kafka_cash_trx;