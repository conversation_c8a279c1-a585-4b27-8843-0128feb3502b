spring:
  application:
    name: cash-processing
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    pro_cash_in_topic: ${PRO_INTERNAL_CASH_TOPIC:PRO.INTERNAL.CASH}
    cash_processing_in_group: ${CASH_PROCESSING_IN_GROUP:cash_processing_in_group}
    fiscal_in_topic: ${FISCAL_INPUT_TOPIC:FISCAL.IN}
    cash_trx_in_topic: ${CASH_IN_TOPIC:CASH.TRX}
    pro_error_out_topic: ${PRO_ERROR_OUT_TOPIC:PRO.ERROR.OUT}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://click-0.tkp2.prod:8123/dev}

server:
  port: 8080
