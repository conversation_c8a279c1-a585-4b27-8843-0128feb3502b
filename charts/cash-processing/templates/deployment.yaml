apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cash-processing.fullname" . }}
  namespace: {{ include "cash-processing.fullname" . }}
  labels:
    {{- include "cash-processing.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "cash-processing.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "cash-processing.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "cash-processing.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-jar", "cash-processing.jar"]
          env:
            - name: CLIENT_LOGGING_ENABLE
              value: {{ ternary "true" "false" .Values.env.client.logging.enable | quote }}
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
            - name: R2DB_URL
              valueFrom:
                secretKeyRef:
                  name: database
                  key: r2url
            - name: CLICKHOUSE_URL
              valueFrom:
                secretKeyRef:
                  name: database
                  key: clickhouseUrl
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
