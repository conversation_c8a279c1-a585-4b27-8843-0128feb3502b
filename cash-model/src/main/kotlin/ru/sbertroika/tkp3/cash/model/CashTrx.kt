package ru.sbertroika.tkp3.cash.model

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.ZonedDateTime
import java.util.*

@Table("cash_trx")
data class CashTrx(

    /**
     * Идентификатор транзакции
     */
    @Id
    @Column("trx_id")
    val trxId: UUID,

    /**
     * Время проведения операции на терминале
     */
    @Column("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime,

    /**
     * Время формирования транзакции на сервере
     */
    @Column("record_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    var recordAt: ZonedDateTime? = null,

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: UUID? = null,

    /**
     * Порядковый номер операции на терминале (уникальный в рамках смены)
     */
    val ern: Int,

    /**
     * Номер смены на терминале
     */
    @Column("shift_num")
    var shiftNum: Int? = null,

    /**
     * Идентификатор организации
     */
    @Column("org_id")
    var orgId: UUID? = null,

    /**
     * Номер маршрута
     */
    @Column("route_number")
    val routeNumber: String,

    /**
     * Номер Т/С
     */
    @Column("vehicle_number")
    val vehicleNumber: String? = null,

    /**
     * ФИО кассира/кондуктора
     */
    @Column("cashier_name")
    val cashierName: String,

    /**
     * ФИО водителя
     */
    @Column("driver_name")
    val driverName: String,

    /**
     * Стоимость
     */
    @Column("amount")
    val amount: Int,

    /**
     * Кол-во билетов
     */
    @Column("ticket_count")
    val ticketCount: Int,

    /**
     * Заводской номер терминала
     */
    @Column("terminal_serial")
    var terminalSerial: String? = null
)
